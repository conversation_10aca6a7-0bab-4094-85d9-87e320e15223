/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {
      // Enhanced browser support for better compatibility
      overrideBrowserslist: [
        'defaults',
        'not IE 11',
        'not op_mini all',
        'last 2 versions',
        '> 1%'
      ],
      grid: 'no-autoplace',
      flexbox: 'no-2009'
    },
    // Production optimizations
    ...(process.env.NODE_ENV === 'production' && {
      cssnano: {
        preset: ['default', {
          // Preserve important comments and source maps
          discardComments: {
            removeAll: false,
          },
          // Safe minification settings for modern CSS
          normalizeWhitespace: true,
          mergeLonghand: true,
          mergeRules: true,
          reduceIdents: false, // Preserve animation names
          zindex: false, // Don't modify z-index values
        }]
      }
    })
  },
};

export default config;
