import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

interface BlogPost {
  slug: string
  title: string
  category: string
  readTime: string
  publishDate: string
}

interface BlogSidebarProps {
  currentSlug: string
}

const blogPosts: BlogPost[] = [
  {
    slug: "boosting-revenue-overnight-with-text-message-reminders-for-medical-appointments",
    title: "Boosting Revenue Overnight with Text Message Reminders for Medical Appointments",
    category: "Patient Engagement",
    readTime: "8 min read",
    publishDate: "2025-01-15",
  },
  {
    slug: "navigating-through-value-propositions-deciphering-the-offer-in-holistic-healthcare-marketing",
    title: "Navigating Through Value Propositions: Deciphering The Offer in Holistic Healthcare Marketing",
    category: "Marketing Strategy",
    readTime: "6 min read",
    publishDate: "2025-01-10",
  },
  {
    slug: "power-of-unique-value-proposition-and-offer-value-proposition-in-healthcare-marketing",
    title: "The Power Intersection of Unique Value Proposition and Offer Value Proposition in Healthcare Marketing",
    category: "Marketing Strategy",
    readTime: "7 min read",
    publishDate: "2025-01-05",
  },
  {
    slug: "clear-communication-in-marketing-alternative-healthcare-services",
    title: "Understanding the Importance of Clear Communication in Marketing Alternative Healthcare Services",
    category: "Communication",
    readTime: "5 min read",
    publishDate: "2025-01-01",
  },
]

export function BlogSidebar({ currentSlug }: BlogSidebarProps) {
  const otherPosts = blogPosts.filter(post => post.slug !== currentSlug)

  return (
    <aside className="w-full lg:w-80 space-y-6">
      <div className="sticky top-6">
        <h2 className="text-xl font-bold text-slate-900 mb-4">More Articles</h2>
        <div className="space-y-4">
          {otherPosts.map((post) => (
            <Card key={post.slug} className="group hover:shadow-lg transition-all duration-300">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="secondary" className="bg-healing-green/10 text-healing-green hover:bg-healing-green/20 text-xs px-2 py-1">
                    {post.category}
                  </Badge>
                  <span className="text-xs text-slate-500">{post.readTime}</span>
                </div>
                <CardTitle className="text-sm font-semibold text-slate-900 group-hover:text-strategy-blue transition-colors duration-200 leading-tight">
                  <Link href={`/blog/${post.slug}`} className="hover:underline">
                    {post.title}
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <time className="text-xs text-slate-500">
                  {new Date(post.publishDate).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </time>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </aside>
  )
}