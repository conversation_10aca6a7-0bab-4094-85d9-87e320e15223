# Wellness Marketing Maestros - Color Palette Implementation Guide

## Brand Color Palette

Our color palette is sophisticated, professional, and strategic. It is built on a foundation of trust-building blue and serious charcoal, with a single, potent accent for highlighting key insights and actions.

### Primary Colors (25% usage)
These two colors are the workhorses of our brand. They convey trust, intelligence, and professionalism.

- **Strategy Blue**: `#5971E8` (RGB: 89, 113, 232)
- **Charcoal**: `#333333` (RGB: 51, 51, 51)

### Secondary Colors
Our secondary palette provides a sophisticated accent for CTAs and a range of neutrals for clean, premium layouts.

- **Insight Gold (Accent)**: `#E8B95A` (RGB: 232, 185, 90) - Use sparingly for CTAs (5% usage)
- **Cloud Grey**: `#EAEAEA` (RGB: 234, 234, 234)
- **Background White**: `#FBFBFB` (RGB: 251, 251, 251) - Primary neutral (70% usage)
- **White**: `#FFFFFF` (RGB: 255, 255, 255)

### Color Usage & Ratios
To maintain our premium and uncluttered aesthetic, we adhere to a strict color ratio:

- **70% Neutrals** (Background White / Cloud Grey): Our design should feel spacious, clean, and focused
- **25% Primary** (Strategy Blue / Charcoal): Use for text, key structural elements, and branded backgrounds
- **5% Accent** (Insight Gold): Use sparingly for the most important calls-to-action to maximize impact

## Implementation

### Tailwind CSS Classes

#### Brand Colors
```css
/* Primary Colors */
.bg-strategy-blue      /* #5971E8 */
.text-strategy-blue
.border-strategy-blue

.bg-charcoal          /* #333333 */
.text-charcoal
.border-charcoal

/* Secondary Colors */
.bg-insight-gold      /* #E8B95A */
.text-insight-gold
.border-insight-gold

.bg-cloud-grey        /* #EAEAEA */
.text-cloud-grey
.border-cloud-grey

.bg-background-white  /* #FBFBFB */
```

#### Color Variants
Each brand color includes variants (50-900) for different use cases:
```css
.bg-strategy-blue-50   /* Lightest */
.bg-strategy-blue-100
.bg-strategy-blue-200
/* ... */
.bg-strategy-blue-900  /* Darkest */
```

#### Semantic Colors
For components and UI elements, use semantic color classes that automatically adapt to light/dark mode:
```css
.bg-primary           /* Maps to Strategy Blue */
.bg-secondary         /* Maps to Cloud Grey */
.bg-accent           /* Maps to Insight Gold */
.bg-background       /* Maps to Background White */
.text-foreground     /* Maps to Charcoal */
```

### CSS Variables
The color palette is also available as CSS variables:
```css
:root {
  --strategy-blue: 89 113 232;
  --charcoal: 51 51 51;
  --insight-gold: 232 185 90;
  --cloud-grey: 234 234 234;
  --background-white: 251 251 251;
  --white: 255 255 255;
}
```

## Usage Examples

### Buttons
```jsx
// Primary CTA (Strategy Blue)
<Button className="bg-strategy-blue hover:bg-strategy-blue-600">
  Get Started
</Button>

// Accent CTA (Insight Gold - use sparingly)
<Button className="bg-insight-gold hover:bg-insight-gold-600 text-charcoal">
  Learn More
</Button>

// Secondary (Cloud Grey)
<Button variant="secondary">
  Cancel
</Button>
```

### Cards and Containers
```jsx
// Clean card with subtle border
<Card className="bg-white border-cloud-grey hover:border-strategy-blue-200">
  <CardContent>
    <h3 className="text-charcoal">Card Title</h3>
    <p className="text-charcoal-500">Card description</p>
  </CardContent>
</Card>
```

### Typography
```jsx
// Headings use Charcoal by default (defined in globals.css)
<h1>This will be Charcoal color</h1>

// Links use Strategy Blue
<Link href="/page" className="text-strategy-blue hover:text-strategy-blue-600">
  Link Text
</Link>
```

## Dark Mode Support

The color palette automatically adapts for dark mode:
- Background becomes Charcoal
- Text becomes White
- Cards use darker variants
- Accent colors remain vibrant for contrast

## Files Modified

1. **`app/globals.css`** - Added Tailwind directives and brand color CSS variables
2. **`tailwind.config.ts`** - Extended theme with brand color definitions and variants
3. **`components/ui/button.tsx`** - Updated to use brand colors
4. **`app/page.tsx`** - Updated hardcoded colors to use brand palette
5. **Removed** `styles/globals.css` - Eliminated duplicate/unused file

## Best Practices

1. **Follow the 70-25-5 rule** for color distribution
2. **Use semantic classes** (primary, secondary, accent) for components
3. **Use brand color classes** (strategy-blue, charcoal, etc.) for specific brand elements
4. **Test in both light and dark modes** to ensure proper contrast
5. **Use Insight Gold sparingly** - only for the most important CTAs
6. **Prefer Background White over pure White** for main backgrounds
