"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Mail, Phone, MapPin, Clock, ArrowRight, CheckCircle, Shield } from "lucide-react"
import Link from "next/link"

export default function ContactPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = (formData: FormData) => {
    const newErrors: Record<string, string> = {}
    
    const firstName = formData.get('firstName') as string
    const lastName = formData.get('lastName') as string
    const email = formData.get('email') as string
    const company = formData.get('company') as string
    const interest = formData.get('interest') as string

    if (!firstName?.trim()) newErrors.firstName = 'First name is required'
    if (!lastName?.trim()) newErrors.lastName = 'Last name is required'
    if (!email?.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email address'
    }
    if (!company?.trim()) newErrors.company = 'Practice/Company name is required'
    if (!interest) newErrors.interest = 'Please select your primary interest'

    return newErrors
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    const formData = new FormData(e.currentTarget)
    const validationErrors = validateForm(formData)
    
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)
      setIsSubmitting(false)
      return
    }

    setErrors({})
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      setSubmitted(true)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-background-white flex items-center justify-center">
        <div className="max-w-2xl mx-auto text-center p-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-charcoal mb-4">Thank You!</h1>
          <p className="text-xl text-charcoal-500 mb-6">
            Your inquiry has been submitted successfully. A senior strategist will review your submission and respond within 1 business day.
          </p>
          <Button asChild>
            <Link href="/">Return to Homepage</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-background-white to-white py-20 lg:py-32">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-strategy-blue border-strategy-blue-200 bg-strategy-blue-50">
              Begin a Strategic Partnership
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-charcoal mb-8 leading-tight">
              Let's Begin the Conversation
            </h1>
          </div>
        </div>
      </section>

      {/* First Step Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-6">
              We Look Forward to Learning About Your Vision.
            </h2>
            <p className="text-xl text-charcoal-500 mb-8 leading-relaxed">
              Reaching out to us is the first step in a strategic discussion about the future of your practice. We are
              here to answer your questions and determine if a partnership is the right fit to achieve your ambitious
              goals.
            </p>
            <div className="flex flex-wrap items-center justify-center gap-6 text-charcoal-500">
              <div className="flex items-center">
                <Clock className="h-5 w-5 mr-2 text-strategy-blue" />
                <span>Monday - Friday, 9:00 AM - 5:00 PM ET</span>
              </div>
              <div className="flex items-center">
                <ArrowRight className="h-5 w-5 mr-2 text-strategy-blue" />
                <span>Response within 1 business day</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-20 bg-cloud-grey-50">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-4">Send Us a Message</h2>
              <p className="text-xl text-charcoal-500 leading-relaxed">
                This form is the most efficient way to begin our conversation. The details you provide will allow us to
                direct your inquiry to the appropriate senior strategist for a prompt and productive response.
              </p>
            </div>

            <Card className="p-8 bg-white border-2">
              <CardContent className="p-0">
                <form className="space-y-6" aria-label="Contact form" onSubmit={handleSubmit}>
                  <fieldset className="grid md:grid-cols-2 gap-6">
                    <legend className="sr-only">Personal Information</legend>
                    <div className="space-y-2">
                      <Label htmlFor="firstName" className="text-charcoal-700 font-medium">
                        First Name <span className="text-red-500" aria-label="required">*</span>
                      </Label>
                      <Input 
                        id="firstName" 
                        name="firstName"
                        placeholder="Enter your first name" 
                        className={`border-cloud-grey mobile-form-input enhanced-focus ${errors.firstName ? 'border-red-500 focus:border-red-500' : ''}`}
                        required
                        aria-describedby="firstName-error"
                        aria-invalid={!!errors.firstName}
                        autoComplete="given-name"
                      />
                      {errors.firstName && (
                        <div id="firstName-error" className="text-red-500 text-sm" role="alert" aria-live="polite">
                          {errors.firstName}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName" className="text-charcoal-700 font-medium">
                        Last Name <span className="text-red-500" aria-label="required">*</span>
                      </Label>
                      <Input 
                        id="lastName" 
                        name="lastName"
                        placeholder="Enter your last name" 
                        className={`border-cloud-grey ${errors.lastName ? 'border-red-500 focus:border-red-500' : ''}`}
                        required
                        aria-describedby="lastName-error"
                        aria-invalid={!!errors.lastName}
                        autoComplete="family-name"
                      />
                      {errors.lastName && (
                        <div id="lastName-error" className="text-red-500 text-sm" role="alert" aria-live="polite">
                          {errors.lastName}
                        </div>
                      )}
                    </div>
                  </fieldset>
                  
                  <fieldset className="space-y-6">
                    <legend className="sr-only">Contact Information</legend>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-charcoal-700 font-medium">
                        Work Email <span className="text-red-500" aria-label="required">*</span>
                      </Label>
                      <Input 
                        id="email" 
                        name="email"
                        type="email" 
                        placeholder="Enter your work email" 
                        className={`border-cloud-grey ${errors.email ? 'border-red-500 focus:border-red-500' : ''}`}
                        required
                        aria-describedby="email-error"
                        aria-invalid={!!errors.email}
                        autoComplete="email"
                      />
                      {errors.email && (
                        <div id="email-error" className="text-red-500 text-sm" role="alert" aria-live="polite">
                          {errors.email}
                        </div>
                      )}
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="company" className="text-charcoal-700 font-medium">
                          Practice / Company Name <span className="text-red-500" aria-label="required">*</span>
                        </Label>
                        <Input 
                          id="company" 
                          name="company"
                          placeholder="Enter your practice name" 
                          className={`border-cloud-grey ${errors.company ? 'border-red-500 focus:border-red-500' : ''}`}
                          required
                          aria-describedby="company-error"
                          aria-invalid={!!errors.company}
                          autoComplete="organization"
                        />
                        {errors.company && (
                          <div id="company-error" className="text-red-500 text-sm" role="alert" aria-live="polite">
                            {errors.company}
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="website" className="text-charcoal-700 font-medium">
                          Website URL <span className="text-charcoal-400 text-sm">(optional)</span>
                        </Label>
                        <Input 
                          id="website" 
                          name="website"
                          type="url"
                          placeholder="https://yourwebsite.com" 
                          className="border-cloud-grey" 
                          autoComplete="url"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="interest" className="text-charcoal-700 font-medium">
                        I am interested in: <span className="text-red-500" aria-label="required">*</span>
                      </Label>
                      <Select name="interest" required>
                        <SelectTrigger className={`border-cloud-grey ${errors.interest ? 'border-red-500 focus:border-red-500' : ''}`} aria-describedby="interest-error" aria-invalid={!!errors.interest}>
                          <SelectValue placeholder="Select your primary interest" />
                        </SelectTrigger>
                        <SelectContent>
                        <SelectItem value="growth-audit" className="leading-relaxed">
                          Inquiring about a Growth Audit & Strategic Roadmap
                        </SelectItem>
                        <SelectItem value="opportunity-analysis" className="leading-relaxed">
                          Requesting a Complimentary Patient Acquisition Opportunity Analysis
                        </SelectItem>
                        <SelectItem value="press-speaking" className="leading-relaxed">
                          A Press or Speaking Engagement Inquiry
                        </SelectItem>
                        <SelectItem value="other" className="leading-relaxed">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.interest && (
                        <div id="interest-error" className="text-red-500 text-sm" role="alert" aria-live="polite">
                          {errors.interest}
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message" className="text-charcoal-700 font-medium">
                        Message <span className="text-charcoal-400 text-sm">(optional)</span>
                      </Label>
                      <Textarea
                        id="message"
                        name="message"
                        placeholder="Please provide a brief overview of your practice and your primary growth objective."
                        className="border-cloud-grey min-h-[120px]"
                        aria-describedby="message-help"
                      />
                      <p id="message-help" className="text-sm text-charcoal-500">
                        Help us understand your practice better to provide the most relevant response.
                      </p>
                    </div>
                  </fieldset>

                  <div className="pt-4">
                    <Button 
                      type="submit" 
                      size="lg" 
                      variant="accent-contrast"
                      className="w-full mobile-form-button"
                      disabled={isSubmitting}
                      aria-describedby="submit-help"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          Begin the Conversation
                          <ArrowRight className="ml-2 h-5 w-5" />
                        </>
                      )}
                    </Button>
                    <p id="submit-help" className="text-sm text-charcoal-500 text-center mt-3">
                      We'll respond within 1 business day with next steps.
                    </p>
                    
                    {/* Trust indicators */}
                    <div className="flex flex-wrap justify-center items-center gap-4 mt-4 text-xs text-charcoal-400">
                      <div className="flex items-center">
                        <Shield className="h-4 w-4 mr-1 text-strategy-blue" />
                        <span>SSL Secured</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-1 text-strategy-blue" />
                        <span>HIPAA Compliant</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1 text-strategy-blue" />
                        <span>24hr Response</span>
                      </div>
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Alternative Contact Methods */}
      <section className="py-20">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-12 text-center">Other Ways to Connect</h2>

            <div className="grid lg:grid-cols-3 gap-8">
              <Card className="p-6 lg:p-8 text-center border-2 hover:border-strategy-blue-200 transition-colors min-h-[300px] flex flex-col">
                <CardContent className="p-0 flex-1 flex flex-col">
                  <div className="w-16 h-16 bg-strategy-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Mail className="h-8 w-8 text-strategy-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-strategy-blue mb-4">Email Our Partnership Team</h3>
                  <p className="text-charcoal-500 mb-4 flex-1">
                    For direct inquiries, you can reach our strategic partnerships desk at:
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-strategy-blue font-medium hover:underline break-all text-sm lg:text-base"
                  >
                    <EMAIL>
                  </a>
                </CardContent>
              </Card>

              <Card className="p-6 lg:p-8 text-center border-2 hover:border-strategy-blue-200 transition-colors min-h-[300px] flex flex-col">
                <CardContent className="p-0 flex-1 flex flex-col">
                  <div className="w-16 h-16 bg-strategy-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Phone className="h-8 w-8 text-strategy-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-strategy-blue mb-4">For Scheduled Calls</h3>
                  <p className="text-charcoal-500 mb-4 leading-relaxed flex-1">
                    To ensure every prospective partner receives our undivided attention, we conduct initial discussions
                    by appointment. Please use the form above to schedule a discovery call with one of our strategists.
                  </p>
                  <p className="text-charcoal-500 font-medium">[Phone Number Placeholder]</p>
                </CardContent>
              </Card>

              <Card className="p-6 lg:p-8 text-center border-2 hover:border-strategy-blue-200 transition-colors min-h-[300px] flex flex-col">
                <CardContent className="p-0 flex-1 flex flex-col">
                  <div className="w-16 h-16 bg-strategy-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <MapPin className="h-8 w-8 text-strategy-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-strategy-blue mb-4">Our Headquarters</h3>
                  <div className="text-charcoal-500 space-y-1 flex-1 flex flex-col justify-center">
                    <p>[Street Address]</p>
                    <p>[City, State, ZIP Code]</p>
                    <p>[Country]</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* What to Expect Next */}
      <section className="py-20 bg-cloud-grey-50">
        <div className="container mx-auto px-4 max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl lg:text-4xl font-bold text-strategy-blue mb-6 text-center">
              A Different Kind of Follow-Up
            </h2>
            <div className="bg-white rounded-lg p-8 border-2">
              <p className="text-xl text-charcoal-500 mb-6 leading-relaxed">
                Once you submit your inquiry, you will not be added to a generic mailing list or contacted by a junior
                salesperson.
              </p>
              <p className="text-lg text-charcoal-500 leading-relaxed">
                Your submission will be personally reviewed by a senior strategist. They will reach out to you within
                one business day to either answer your questions directly or schedule a complimentary, 30-minute
                introductory call to explore a potential partnership in more detail.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
