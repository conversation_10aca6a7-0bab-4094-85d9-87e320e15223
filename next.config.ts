import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Production-ready configuration for Next.js 15
  async redirects() {
    return [
      {
        source: '/navigating-through-value-propositions-deciphering-the-offer-in-holistic-healthcare-marketing/:path*',
        destination: '/blog/navigating-through-value-propositions-deciphering-the-offer-in-holistic-healthcare-marketing',
        permanent: true,
      },
      {
        source: '/boosting-revenue-overnight-with-text-message-reminders-for-medical-appointments/:path*',
        destination: '/blog/boosting-revenue-overnight-with-text-message-reminders-for-medical-appointments',
        permanent: true,
      },
      {
        source: '/power-of-unique-value-proposition-and-offer-value-proposition-in-healthcare-marketing/:path*',
        destination: '/blog/power-of-unique-value-proposition-and-offer-value-proposition-in-healthcare-marketing',
        permanent: true,
      },
      {
        source: '/clear-communication-in-marketing-alternative-healthcare-services/:path*',
        destination: '/blog/clear-communication-in-marketing-alternative-healthcare-services',
        permanent: true,
      },
    ];
  },
};

export default nextConfig;